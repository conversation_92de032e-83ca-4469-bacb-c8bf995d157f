# AuraValidationNetwork Deployment Configuration
# Copy this file to .env and fill in your values

# REQUIRED: Private key for deployment (without 0x prefix)
# Get this from your Ronin wallet (MetaMask, etc.)
PRIVATE_KEY=your_private_key_here

# OPTIONAL: NXS Token contract address for bounty payments
# Leave as zero address for testing, update for production
NXS_TOKEN_ADDRESS=******************************************

# OPTIONAL: Ronin API key for contract verification
# Get from Ronin explorer if you want to verify contracts
RONIN_API_KEY=your_ronin_api_key_here

# Network Configuration (usually don't need to change these)
RONIN_TESTNET_RPC=https://saigon-testnet.roninchain.com/rpc
RONIN_MAINNET_RPC=https://api.roninchain.com/rpc

# Deployment Settings
GAS_PRICE=20000000000
GAS_LIMIT=8000000

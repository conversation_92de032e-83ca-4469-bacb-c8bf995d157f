<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#1f2937">
    <title>Aura Mesh - Ronin Blockchain Mobile Validator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script async src="https://unpkg.com/es-module-shims@1.10.0/dist/es-module-shims.js"></script>
    <script type="importmap">
    {
      "imports": {
        "three": "https://cdn.jsdelivr.net/npm/three@0.166.1/build/three.module.js",
        "react": "https://esm.sh/react@18.3.1",
        "react-dom/client": "https://esm.sh/react-dom@18.3.1/client"
      }
    }
    </script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #111827;
            color: #f3f4f6;
            overflow: hidden;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
            touch-action: manipulation;
        }
        #root {
            width: 100vw;
            height: 100vh;
            height: 100dvh;
        }
        
        .mobile-panel {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px 24px 0 0;
            padding: 24px;
            transform: translateY(0);
            transition: transform 0.3s ease;
            z-index: 50;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .mobile-panel.collapsed {
            transform: translateY(calc(100% - 80px));
        }
        
        .mesh-pulse {
            animation: meshPulse 2s infinite;
        }
        
        @keyframes meshPulse {
            0% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
            100% { opacity: 0.6; transform: scale(1); }
        }
        
        .touch-target {
            min-height: 44px;
            min-width: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .mobile-toggle {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        
        .mobile-toggle input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .mobile-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #374151;
            transition: .4s;
            border-radius: 34px;
        }
        
        .mobile-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .mobile-slider {
            background-color: #10b981;
        }
        
        input:checked + .mobile-slider:before {
            transform: translateX(26px);
        }
        
        .bluetooth-indicator {
            animation: bluetoothScan 3s infinite;
        }
        
        @keyframes bluetoothScan {
            0%, 100% { opacity: 0.4; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel" data-type="module">
        import React, { useState, useEffect, useRef } from 'react';
        import { createRoot } from 'react-dom/client';
        import * as THREE from 'three';

        // Mobile Bluetooth Mesh Visualization
        const MobileMeshVisualization = ({ engineStatus, bluetoothEnabled, nearbyDevices }) => {
            const mountRef = useRef(null);

            useEffect(() => {
                if (!mountRef.current) return;

                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
                camera.position.z = 6;

                // Create renderer with better context management
                const renderer = new THREE.WebGLRenderer({
                    antialias: true,
                    alpha: true,
                    preserveDrawingBuffer: false,
                    powerPreference: "default"
                });
                renderer.setSize(window.innerWidth, window.innerHeight);
                renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // Limit pixel ratio for performance
                mountRef.current.appendChild(renderer.domElement);

                // Central device (user's phone)
                const phoneGeometry = new THREE.BoxGeometry(0.3, 0.6, 0.1);
                const phoneMaterial = new THREE.MeshStandardMaterial({
                    color: engineStatus.isRunning ? '#10b981' : '#6b7280',
                    emissive: engineStatus.isRunning ? '#10b981' : '#6b7280',
                    emissiveIntensity: 0.3,
                });
                const phone = new THREE.Mesh(phoneGeometry, phoneMaterial);
                scene.add(phone);

                // Bluetooth mesh peers (nearby devices)
                const peerGroup = new THREE.Group();
                nearbyDevices.forEach((device, index) => {
                    const angle = (index / nearbyDevices.length) * Math.PI * 2;
                    const radius = 3;
                    
                    const peerGeometry = new THREE.SphereGeometry(0.1, 8, 8);
                    const peerMaterial = new THREE.MeshStandardMaterial({
                        color: device.connected ? '#3b82f6' : '#9ca3af',
                        emissive: device.connected ? '#3b82f6' : '#9ca3af',
                        emissiveIntensity: 0.2,
                    });
                    const peer = new THREE.Mesh(peerGeometry, peerMaterial);
                    
                    peer.position.set(
                        Math.cos(angle) * radius,
                        Math.sin(angle) * radius,
                        (Math.random() - 0.5) * 1
                    );

                    // Connection lines for Bluetooth mesh
                    if (device.connected && bluetoothEnabled) {
                        const lineGeometry = new THREE.BufferGeometry().setFromPoints([
                            new THREE.Vector3(0, 0, 0),
                            peer.position
                        ]);
                        const lineMaterial = new THREE.LineBasicMaterial({
                            color: '#3b82f6',
                            opacity: 0.6,
                            transparent: true
                        });
                        const line = new THREE.Line(lineGeometry, lineMaterial);
                        peerGroup.add(line);
                    }

                    peerGroup.add(peer);
                });
                scene.add(peerGroup);

                // Lighting
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.2);
                scene.add(ambientLight);
                const pointLight = new THREE.PointLight(0xffffff, 1);
                pointLight.position.set(3, 3, 3);
                scene.add(pointLight);

                // Animation
                let clock = new THREE.Clock();
                let animationId;
                const animate = () => {
                    animationId = requestAnimationFrame(animate);
                    const delta = clock.getDelta();
                    const time = clock.getElapsedTime();

                    // Animate phone
                    if (engineStatus.isRunning) {
                        phone.rotation.y += delta * 0.5;
                        phoneMaterial.emissiveIntensity = 0.3 + Math.sin(time * 2) * 0.2;
                    }

                    // Animate mesh network
                    peerGroup.rotation.y += delta * 0.1;

                    renderer.render(scene, camera);
                };
                animate();

                // Enhanced cleanup function
                const mount = mountRef.current;
                return () => {
                    // Cancel animation frame
                    if (animationId) {
                        cancelAnimationFrame(animationId);
                    }

                    // Dispose of Three.js resources
                    scene.traverse((object) => {
                        if (object.geometry) {
                            object.geometry.dispose();
                        }
                        if (object.material) {
                            if (Array.isArray(object.material)) {
                                object.material.forEach(material => material.dispose());
                            } else {
                                object.material.dispose();
                            }
                        }
                    });

                    // Dispose renderer and remove from DOM
                    if (renderer) {
                        renderer.dispose();
                        if (mount && mount.contains(renderer.domElement)) {
                            mount.removeChild(renderer.domElement);
                        }
                    }

                    // Clear scene
                    scene.clear();
                };
            }, [engineStatus.isRunning, bluetoothEnabled, nearbyDevices]);

            return <div ref={mountRef} className="absolute top-0 left-0 w-full h-full" />;
        };

        // Permission Dialog for Mobile
        const MobilePermissionDialog = ({ isOpen, onAccept, onDecline }) => {
            if (!isOpen) return null;

            return (
                <div className="fixed inset-0 bg-black bg-opacity-95 flex items-end justify-center z-50">
                    <div className="bg-gray-800 rounded-t-3xl p-6 w-full max-w-lg border-t border-gray-600">
                        <div className="flex flex-col items-center mb-6">
                            <div className="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center mb-4 mesh-pulse">
                                <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                                </svg>
                            </div>
                            <h2 className="text-2xl font-bold text-white text-center mb-2">Join Ronin Mesh</h2>
                            <p className="text-gray-400 text-center">Turn your phone into a blockchain node</p>
                        </div>
                        
                        <div className="space-y-4 mb-6">
                            <div className="bg-blue-900 rounded-xl p-4">
                                <h3 className="font-semibold text-blue-300 mb-2">📱 Mobile Mesh Network</h3>
                                <p className="text-sm text-blue-100">
                                    Connect with nearby phones via Bluetooth. Keep gaming when WiFi fails.
                                </p>
                            </div>
                            
                            <div className="bg-green-900 rounded-xl p-4">
                                <h3 className="font-semibold text-green-300 mb-2">💰 Earn $RON Tokens</h3>
                                <p className="text-sm text-green-100">
                                    Get rewarded for helping validate transactions and relay data.
                                </p>
                            </div>

                            <div className="bg-gray-700 rounded-xl p-4">
                                <h3 className="font-semibold text-gray-300 mb-2">🔒 Privacy First</h3>
                                <p className="text-sm text-gray-200">
                                    Only uses Bluetooth for mesh. No personal data accessed.
                                </p>
                            </div>
                        </div>

                        <div className="flex flex-col space-y-3">
                            <button
                                onClick={onAccept}
                                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-6 rounded-xl transition-colors"
                            >
                                🚀 Enable Mesh & Earn $RON
                            </button>
                            <button
                                onClick={onDecline}
                                className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-6 rounded-xl transition-colors"
                            >
                                Not Now
                            </button>
                        </div>
                    </div>
                </div>
            );
        };

        // Main Mobile App
        const MobileApp = () => {
            const [engineStatus, setEngineStatus] = useState({
                isConnected: false,
                isRunning: false,
                meshMode: false,
                connectedPeers: 0,
                roninConnected: false,
                tasksProcessed: 0,
                earnings: 0.0245
            });

            const [hasPermission, setHasPermission] = useState(() => {
                return localStorage.getItem('aura-mesh-permission') === 'granted';
            });
            const [showPermissionDialog, setShowPermissionDialog] = useState(!hasPermission);
            const [mobileMenuCollapsed, setMobileMenuCollapsed] = useState(true);
            const [bluetoothEnabled, setBluetoothEnabled] = useState(false);
            const [nearbyDevices, setNearbyDevices] = useState([
                { id: 'device1', connected: true, name: 'Gaming Phone' },
                { id: 'device2', connected: true, name: 'Tablet Pro' },
                { id: 'device3', connected: false, name: 'Laptop' }
            ]);

            const ws = useRef(null);
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            // WebSocket connection to IPC server
            useEffect(() => {
                const connectWebSocket = () => {
                    try {
                        ws.current = new WebSocket('ws://127.0.0.1:9898');

                        ws.current.onopen = () => {
                            console.log('Connected to Aura Engine');
                            setEngineStatus(prev => ({ ...prev, isConnected: true }));

                            // Request initial status
                            ws.current.send(JSON.stringify({ command: 'GetFullStatus' }));
                        };

                        ws.current.onmessage = (event) => {
                            try {
                                const data = JSON.parse(event.data);

                                if (data.type === 'status') {
                                    const status = data.data;
                                    setEngineStatus(prev => ({
                                        ...prev,
                                        isConnected: true,
                                        isRunning: status.is_running,
                                        meshMode: status.mesh_mode,
                                        connectedPeers: status.connected_peers,
                                        roninConnected: status.ronin_connected,
                                        tasksProcessed: status.tasks_processed
                                    }));

                                    // Update nearby devices from mesh peers
                                    if (status.mesh_peers && status.mesh_peers.length > 0) {
                                        setNearbyDevices(status.mesh_peers.map(peer => ({
                                            id: peer.id,
                                            connected: peer.is_connected,
                                            name: `Peer ${peer.id.substring(0, 8)}`
                                        })));
                                    }
                                }
                            } catch (error) {
                                console.error('Error parsing WebSocket message:', error);
                            }
                        };

                        ws.current.onclose = () => {
                            console.log('Disconnected from Aura Engine');
                            setEngineStatus(prev => ({ ...prev, isConnected: false }));

                            // Use requestIdleCallback for better performance, fallback to setTimeout
                            if (window.requestIdleCallback) {
                                window.requestIdleCallback(() => {
                                    setTimeout(connectWebSocket, 3000);
                                });
                            } else {
                                setTimeout(connectWebSocket, 3000);
                            }
                        };

                        ws.current.onerror = (error) => {
                            console.error('WebSocket error:', error);
                        };
                    } catch (error) {
                        console.error('Failed to connect to Aura Engine:', error);
                        if (window.requestIdleCallback) {
                            window.requestIdleCallback(() => {
                                setTimeout(connectWebSocket, 3000);
                            });
                        } else {
                            setTimeout(connectWebSocket, 3000);
                        }
                    }
                };

                connectWebSocket();

                // Request status updates every 5 seconds
                const statusInterval = setInterval(() => {
                    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
                        ws.current.send(JSON.stringify({ command: 'GetFullStatus' }));
                    }
                }, 5000);

                return () => {
                    clearInterval(statusInterval);
                    if (ws.current) {
                        ws.current.close();
                    }
                };
            }, []);

            // Permission handlers
            const handlePermissionAccept = async () => {
                setHasPermission(true);
                setShowPermissionDialog(false);
                localStorage.setItem('aura-mesh-permission', 'granted');
                
                // Request Bluetooth on mobile
                if (isMobile && 'bluetooth' in navigator) {
                    try {
                        await navigator.bluetooth.requestDevice({
                            acceptAllDevices: true,
                            optionalServices: ['battery_service']
                        });
                        setBluetoothEnabled(true);
                    } catch (error) {
                        console.log('Bluetooth permission denied');
                    }
                }
                
                setEngineStatus(prev => ({ ...prev, isRunning: true, meshMode: true }));
            };

            const handlePermissionDecline = () => {
                setShowPermissionDialog(false);
                localStorage.setItem('aura-mesh-permission', 'denied');
            };

            const handleToggle = () => {
                const newRunningState = !engineStatus.isRunning;
                setEngineStatus(prev => ({ ...prev, isRunning: newRunningState }));

                // Send command to engine
                if (ws.current && ws.current.readyState === WebSocket.OPEN) {
                    const command = newRunningState ? 'StartEngine' : 'StopEngine';
                    ws.current.send(JSON.stringify({ command }));
                }
            };

            return (
                <div className="relative w-full h-full bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
                    <MobilePermissionDialog 
                        isOpen={showPermissionDialog}
                        onAccept={handlePermissionAccept}
                        onDecline={handlePermissionDecline}
                    />

                    {hasPermission && (
                        <>
                            <MobileMeshVisualization 
                                engineStatus={engineStatus}
                                bluetoothEnabled={bluetoothEnabled}
                                nearbyDevices={nearbyDevices}
                            />

                            {/* Mobile Status Bar */}
                            <div className="absolute top-0 left-0 right-0 bg-black bg-opacity-50 backdrop-blur-sm p-4 z-40">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        <div className={`w-3 h-3 rounded-full ${engineStatus.isRunning ? 'bg-green-500 animate-pulse' : 'bg-gray-500'}`}></div>
                                        <span className="text-white text-sm font-medium">
                                            {engineStatus.meshMode ? '📡 Mesh Active' : '🔗 Ronin Node'}
                                        </span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        {bluetoothEnabled && (
                                            <div className="flex items-center space-x-1 bluetooth-indicator">
                                                <svg className="w-4 h-4 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M10 2L6 6h3v8H6l4 4 4-4h-3V6h3l-4-4z"/>
                                                </svg>
                                                <span className="text-blue-400 text-xs">{nearbyDevices.filter(d => d.connected).length}</span>
                                            </div>
                                        )}
                                        <button
                                            onClick={() => setMobileMenuCollapsed(!mobileMenuCollapsed)}
                                            className="text-white p-1 touch-target"
                                        >
                                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={mobileMenuCollapsed ? "M4 6h16M4 12h16M4 18h16" : "M6 18L18 6M6 6l12 12"} />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            {/* Mobile Bottom Panel */}
                            <div className={`mobile-panel ${mobileMenuCollapsed ? 'collapsed' : ''}`}>
                                <div 
                                    className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1 w-12 h-1 bg-gray-400 rounded-full cursor-pointer"
                                    onClick={() => setMobileMenuCollapsed(!mobileMenuCollapsed)}
                                ></div>

                                <div className="flex items-center justify-between mb-6">
                                    <div className="flex items-center space-x-3">
                                        <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mesh-pulse">
                                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 className="text-white font-bold text-xl">Aura Mesh</h3>
                                            <p className="text-gray-400 text-sm">{engineStatus.connectedPeers} peers • Earning $RON</p>
                                        </div>
                                    </div>
                                    
                                    <label className="mobile-toggle">
                                        <input
                                            type="checkbox"
                                            checked={engineStatus.isRunning}
                                            onChange={handleToggle}
                                        />
                                        <span className="mobile-slider"></span>
                                    </label>
                                </div>

                                {!mobileMenuCollapsed && (
                                    <>
                                        <div className="grid grid-cols-2 gap-4 mb-6">
                                            <div className="bg-gray-800 rounded-xl p-4">
                                                <div className="text-2xl font-bold text-white">{nearbyDevices.filter(d => d.connected).length}</div>
                                                <div className="text-gray-400 text-sm">Connected Devices</div>
                                            </div>
                                            <div className="bg-gray-800 rounded-xl p-4">
                                                <div className="text-2xl font-bold text-green-400">${engineStatus.earnings}</div>
                                                <div className="text-gray-400 text-sm">RON Earned Today</div>
                                            </div>
                                        </div>

                                        <div className="space-y-3">
                                            <h4 className="text-white font-semibold">Nearby Devices</h4>
                                            {nearbyDevices.map((device, index) => (
                                                <div key={device.id} className="flex items-center justify-between bg-gray-800 rounded-lg p-3">
                                                    <div className="flex items-center space-x-3">
                                                        <div className={`w-3 h-3 rounded-full ${device.connected ? 'bg-green-500' : 'bg-gray-500'}`}></div>
                                                        <span className="text-white text-sm">{device.name}</span>
                                                    </div>
                                                    <span className="text-gray-400 text-xs">
                                                        {device.connected ? 'Connected' : 'Available'}
                                                    </span>
                                                </div>
                                            ))}
                                        </div>
                                    </>
                                )}
                            </div>
                        </>
                    )}
                </div>
            );
        };

        const container = document.getElementById('root');
        const root = createRoot(container);
        root.render(<MobileApp />);
    </script>
</body>
</html>

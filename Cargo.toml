[package]
name = "aura-validation-network"
version = "0.0.1"
edition = "2021"
description = "Ronin Blockchain Mesh Utility for Offline Transactions"

[lib]
name = "aura_validation_network"
path = "src/lib.rs"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
# Asynchronous runtime for managing concurrent tasks (like networking and validation)
tokio = { version = "1", features = ["full"] }

# Foundational library for building peer-to-peer networks
libp2p = { version = "0.53", features = ["tokio", "gossipsub", "mdns", "tcp", "noise", "yamux", "macros"] }

# Bluetooth Low Energy (BLE) library for mesh networking
btleplug = { version = "0.11", features = ["serde"] }

# For secure WebSocket communication with the web portal
tokio-tungstenite = "0.21"
futures-util = { version = "0.3", default-features = false, features = ["sink", "std"] }

# Cryptography: hashing (SHA-3) and digital signatures (ed25519)
sha3 = "0.10"
ed25519-dalek = { version = "2.0", features = ["serde"] }
# For generating random numbers needed for key creation
rand = "0.8"
serde = { version = "1.0", features = ["derive"] } # For serializing/deserializing data
serde_json = "1.0" # For JSON serialization/deserialization
config = "0.13" # For configuration management
toml = "0.8" # For TOML serialization
tracing = "0.1" # For structured, event-based logging
tracing-subscriber = "0.3" # To configure how logs are processed and displayed

# Web3 and Ronin blockchain integration
web3 = "0.19"
ethers = { version = "2.0", features = ["rustls"] }

# Data persistence for offline transaction queue
sled = "0.34" # Embedded database for transaction storage
uuid = { version = "1.0", features = ["v4", "serde"] } # For unique identifiers

# Additional serialization and networking
bincode = "1.3" # Efficient binary serialization
thiserror = "1.0" # Better error handling
anyhow = "1.0" # Flexible error handling
hex = "0.4" # Hexadecimal encoding/decoding
once_cell = "1.19" # Global static variables
chrono = { version = "0.4", features = ["serde"] } # Date and time handling

# NEW DEPENDENCIES FOR FINAL 5% IMPLEMENTATION
# GPU Processing and Advanced Computing
wgpu = "0.17"
async-trait = "0.1"
futures-intrusive = "0.5"
bytemuck = { version = "1.0", features = ["derive"] }

# Advanced Cryptography and Security
aes-gcm = "0.10"
chacha20poly1305 = "0.10"
lru = "0.12"

# Performance and Caching
dashmap = "5.5"
parking_lot = "0.12"

# System Information and Monitoring
sysinfo = "0.29"


[dev-dependencies]
tempfile = "3.0" # Temporary files for testing
tokio-test = "0.4" # Testing utilities for async code
